# OnlyMonster Automations - Test Scripts

This directory contains all test scripts, debugging utilities, and preview tools for the OnlyMonster automation system.

## 📋 Test Scripts Overview

### Core Functionality Tests

#### `test_database.py`
- **Purpose**: Tests database operations and data storage functionality
- **What it tests**:
  - Database initialization
  - Data insertion and retrieval
  - Basic CRUD operations
- **Usage**: `python test_database.py`

#### `test_scraper_init.py`
- **Purpose**: Verifies that the OnlyMonster scraper can initialize properly
- **What it tests**:
  - Chrome WebDriver setup
  - Database connection establishment
  - Basic scraper initialization
- **Usage**: `python test_scraper_init.py`

### Earnings Tracking Tests

#### `test_earnings_tracking.py`
- **Purpose**: Comprehensive test for earnings tracking functionality
- **What it tests**:
  - Data insertion with earnings values
  - Earnings calculation and retrieval
  - Daily earnings growth tracking
  - Analytics integration with earnings data
- **Usage**: `python test_earnings_tracking.py`

#### `test_earnings_extraction.py`
- **Purpose**: Tests the earnings extraction logic from OnlyMonster web interface
- **What it tests**:
  - Parsing earnings text from various formats
  - Handling different currency representations
  - Edge cases and error handling
- **Usage**: `python test_earnings_extraction.py`

## 🔧 Debug & Utility Scripts

### Slack Integration Debugging

#### `debug_slack.py`
- **Purpose**: Comprehensive Slack API debugging and testing
- **Features**:
  - Bot token authentication testing
  - Channel access verification
  - Message sending tests
  - OAuth token validation
- **Usage**: `python debug_slack.py`

### Message Preview Tools

#### `preview_slack_message.py`
- **Purpose**: Preview what Slack messages will look like before sending
- **Features**:
  - Shows formatted message output
  - Tests message formatting logic
  - Useful for debugging message appearance
- **Usage**: `python preview_slack_message.py`

#### `preview_standardized_slack.py`
- **Purpose**: Preview the standardized daily report format
- **Features**:
  - Shows complete standardized report format
  - Uses real data from database
  - Tests all report sections
- **Usage**: `python preview_standardized_slack.py`

## 🚀 Running Tests

### Prerequisites
Make sure you have the virtual environment activated. Tests can be run from either the project root or the tests directory:

```bash
# From project root
cd /root/onlymonster-automations
source venv/bin/activate

# Or from tests directory
cd /root/onlymonster-automations/tests
source ../venv/bin/activate
```

### Running Individual Tests

#### From Project Root Directory:
```bash
# Test database functionality
python tests/test_database.py

# Test scraper initialization
python tests/test_scraper_init.py

# Test earnings tracking
python tests/test_earnings_tracking.py

# Test earnings extraction logic
python tests/test_earnings_extraction.py

# Debug Slack integration
python tests/debug_slack.py

# Preview Slack messages
python tests/preview_slack_message.py
python tests/preview_standardized_slack.py
```

#### From Tests Directory:
```bash
cd tests

# Test database functionality
python test_database.py

# Test scraper initialization
python test_scraper_init.py

# Test earnings tracking
python test_earnings_tracking.py

# Test earnings extraction logic
python test_earnings_extraction.py

# Debug Slack integration
python debug_slack.py

# Preview Slack messages
python preview_slack_message.py
python preview_standardized_slack.py
```

### Running All Tests
```bash
# From project root
for test in tests/test_*.py; do
    echo "Running $test..."
    python "$test"
    echo "---"
done

# From tests directory
for test in test_*.py; do
    echo "Running $test..."
    python "$test"
    echo "---"
done
```

## 📊 Test Categories

### 1. Unit Tests
- `test_database.py` - Database operations
- `test_scraper_init.py` - Scraper initialization
- `test_earnings_extraction.py` - Earnings parsing logic

### 2. Integration Tests
- `test_earnings_tracking.py` - Full earnings workflow
- `debug_slack.py` - Slack API integration

### 3. Preview/Debug Tools
- `preview_slack_message.py` - Message formatting preview
- `preview_standardized_slack.py` - Report format preview

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're running tests from the project root directory
2. **Database Errors**: Ensure the database file exists and is accessible
3. **Slack API Errors**: Check that all Slack tokens are properly configured in `config.py`
4. **WebDriver Errors**: Ensure Chrome and ChromeDriver are properly installed

### Environment Setup
If tests fail due to missing dependencies:

```bash
pip install -r requirements.txt
```

### Configuration
Make sure your `config.py` file has all required settings:
- Database path
- Slack tokens and channel IDs
- OnlyMonster credentials

## 📝 Adding New Tests

When adding new test scripts:

1. Name them with the `test_` prefix
2. Include a docstring explaining the purpose
3. Add error handling and clear output messages
4. Update this README with the new test description

## 🎯 Test Coverage

Current test coverage includes:
- ✅ Database operations
- ✅ Scraper initialization
- ✅ Earnings tracking and extraction
- ✅ Slack integration
- ✅ Message formatting
- ✅ Report generation

Areas for future testing:
- [ ] Full end-to-end scraping workflow
- [ ] Error recovery mechanisms
- [ ] Performance testing
- [ ] Data validation and cleanup
