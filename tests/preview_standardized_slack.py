#!/usr/bin/env python3
"""
Preview the standardized Slack message format
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from standardized_daily_report import get_historical_data, calculate_growth_metrics, format_slack_message

def preview_slack_message():
    """Show what the standardized Slack message looks like"""
    
    print("📱 STANDARDIZED SLACK MESSAGE PREVIEW:")
    print("="*60)
    
    # Get real data
    data_by_link = get_historical_data()
    metrics = calculate_growth_metrics(data_by_link)
    
    # Calculate totals
    total_new_fans = sum(data['fan_growth'] for data in metrics.values())
    total_new_clicks = sum(data['click_growth'] for data in metrics.values())
    stagnant_links = [link for link, data in metrics.items() if data['fan_growth'] == 0 and data['click_growth'] == 0]
    
    report_data = {
        'metrics': metrics,
        'total_new_fans': total_new_fans,
        'total_new_clicks': total_new_clicks,
        'stagnant_links': stagnant_links
    }
    
    # Format message
    message = format_slack_message(report_data)
    
    print(message)
    print("="*60)
    
    print(f"\n📊 KEY FEATURES OF STANDARDIZED REPORT:")
    print("✅ Consistent format every day")
    print("✅ All priority links shown with +fans and +clicks")
    print("✅ Historical deviation analysis")
    print("✅ Performance vs average tracking")
    print("✅ Automatic sorting by performance")
    print("✅ Clear visual indicators (✅⚠️❌📈📉)")
    
    print(f"\n📈 HISTORICAL DATA INCLUDED:")
    print("• 5/17/25 baseline data")
    print("• 5/29/25 mid-period data") 
    print("• 6/4/25 previous period")
    print("• 6/8/25 current period")
    print("• Deviation analysis vs historical averages")
    
    print(f"\n🎯 WHAT'S NEW:")
    print("• Shows performance vs historical daily averages")
    print("• Identifies links performing above/below normal")
    print("• Consistent format regardless of data changes")
    print("• All priority links always shown")
    print("• Clear deviation indicators (📈📉➡️)")

if __name__ == "__main__":
    preview_slack_message()
