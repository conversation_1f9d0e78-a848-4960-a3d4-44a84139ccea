#!/usr/bin/env python3
"""
Enhanced OnlyMonster Scrape & Analysis with Failure Detection
Runs scraping first, then generates reports. Reports failures to Slack if scraping fails.
"""
from onlymonster_scraper import OnlyMonsterScraper
from analytics import TrackingAnalytics
from standardized_daily_report import generate_standardized_report, format_slack_message
from slack_webhook import SlackWebhookNotifier
from config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def send_scraping_failure_alert(error_message):
    """Send a scraping failure alert to Slack"""
    try:
        from datetime import datetime
        
        failure_message = f"""🚨 *OnlyMonster Scraping Failed*
📅 {datetime.now().strftime('%m/%d/%y at %H:%M')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

❌ *Scraping Error*
The daily data collection failed and no new analytics are available.

🔍 *Error Details:*
```
{error_message}
```

⚠️ *Impact:*
• No new tracking data collected today
• Daily report will show stale data
• Manual intervention may be required

🛠️ *Next Steps:*
• Check OnlyMonster website accessibility
• Verify login credentials
• Review scraper logs for detailed errors
• Consider running manual scrape: `python3 scrape_and_analyze.py`

🕐 {datetime.now().strftime('%H:%M %m/%d/%y')}"""

        slack_notifier = SlackWebhookNotifier()
        success = slack_notifier.send_message_via_api(failure_message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            print("✅ Scraping failure alert sent to Slack")
        else:
            print("❌ Failed to send scraping failure alert to Slack")
            
    except Exception as e:
        print(f"❌ Error sending failure alert: {e}")

def main():
    print("🚀 Starting OnlyMonster Scrape & Analysis")
    print("="*60)
    
    # Run scraper
    scraper = OnlyMonsterScraper()
    scraping_successful = False
    
    try:
        data = scraper.run_scraping()
        
        if data and len(data) > 0:
            print(f"\n✅ Successfully scraped {len(data)} tracking links")
            scraping_successful = True
            
            # Run standardized analytics
            print("\n🔍 Running Standardized Analysis...")
            report_data = generate_standardized_report()

            # Send to Slack
            print(f"\n📤 SENDING TO SLACK...")
            print("-" * 25)

            try:
                slack_notifier = SlackWebhookNotifier()
                message = format_slack_message(report_data)

                success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)

                if success:
                    print("✅ Fresh data + standardized report sent to Slack!")
                    print(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
                else:
                    print("❌ Failed to send to Slack")

            except Exception as e:
                print(f"❌ Slack error: {e}")
                print("📧 Report generated locally only")
        else:
            print("❌ No data collected - scraping returned empty results")
            send_scraping_failure_alert("Scraping returned no data - possible website changes or access issues")
            
    except Exception as e:
        print(f"❌ Scraping failed: {e}")
        send_scraping_failure_alert(str(e))
    
    # If scraping failed, don't send a regular report with stale data
    if not scraping_successful:
        print("🚫 Skipping regular report due to scraping failure")
        print("📧 Failure alert sent to Slack instead")

if __name__ == "__main__":
    main()
