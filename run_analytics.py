#!/usr/bin/env python3
"""
Standalone analytics runner for OnlyMonster tracking data
Usage: python3 run_analytics.py [hours_back]
"""
import sys
from analytics import TrackingAnalytics

def main():
    # Get hours back from command line argument, default to 24
    hours_back = 24
    if len(sys.argv) > 1:
        try:
            hours_back = int(sys.argv[1])
        except ValueError:
            print("Invalid hours argument. Using default 24 hours.")
    
    print(f"🔍 Running OnlyMonster Analytics (comparing with {hours_back} hours ago)")
    print("="*80)
    
    analytics = TrackingAnalytics()
    analysis = analytics.run_analysis(hours_back)
    analytics.print_analysis_report(analysis)

if __name__ == "__main__":
    main()
