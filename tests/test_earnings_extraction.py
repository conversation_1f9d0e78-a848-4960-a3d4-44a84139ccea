#!/usr/bin/env python3
"""
Test script to verify the updated earnings extraction logic
"""

import re

def test_earnings_extraction():
    """Test the earnings extraction logic with sample data from the screenshot"""
    
    print("🧪 Testing Updated Earnings Extraction Logic")
    print("=" * 60)
    
    # Sample data from the OnlyMonster screenshot
    test_cases = [
        {
            'name': 'X - naomifoxxxx',
            'earnings_text': '$ 630.40',
            'expected': 630.40
        },
        {
            'name': 'X - nylabaexo', 
            'earnings_text': '$ 283.11',
            'expected': 283.11
        },
        {
            'name': 'instagram-naomifunxo',
            'earnings_text': '$ 38.40',
            'expected': 38.40
        },
        {
            'name': 'reddit-babycheeksx-2',
            'earnings_text': '$ 276.33',
            'expected': 276.33
        },
        {
            'name': 'reels-totallynaomi',
            'earnings_text': '$ 0',
            'expected': 0.00
        },
        {
            'name': 'reddit-babycheeksx',
            'earnings_text': '$ 2,361.50',
            'expected': 2361.50
        },
        {
            'name': 'reels-naominoface',
            'earnings_text': '$ 2,951.43',
            'expected': 2951.43
        },
        {
            'name': 'reddit-babycheeksx02',
            'earnings_text': '$ 4.00',
            'expected': 4.00
        },
        {
            'name': 'reels-lilfoxnaomi-aug-22',
            'earnings_text': '$ 3,459.71',
            'expected': 3459.71
        },
        {
            'name': 'chive-nyla-aug-8',
            'earnings_text': '$ 220.92',
            'expected': 220.92
        },
        {
            'name': 'tiktok-aug-1-24',
            'earnings_text': '$ 2,481.98',
            'expected': 2481.98
        },
        {
            'name': 'chive-aug-1-24',
            'earnings_text': '$ 9,398.86',
            'expected': 9398.86
        },
        {
            'name': 'Reels2024',
            'earnings_text': '$ 20,122.23',
            'expected': 20122.23
        }
    ]
    
    # Test the updated regex pattern
    pattern = r'\$\s*[\d,]+(?:\.\d+)?'
    
    print(f"Using regex pattern: {pattern}")
    print("\nTesting earnings extraction:")
    print("-" * 60)
    
    passed = 0
    failed = 0
    
    for test_case in test_cases:
        name = test_case['name']
        earnings_text = test_case['earnings_text']
        expected = test_case['expected']
        
        # Apply the same logic as in the scraper
        earnings_match = re.search(pattern, earnings_text)
        if earnings_match:
            try:
                # Remove $ and commas and spaces, convert to float
                earnings_str = earnings_match.group().replace('$', '').replace(',', '').replace(' ', '')
                earnings = float(earnings_str)
                
                if abs(earnings - expected) < 0.01:  # Allow for small floating point differences
                    print(f"✅ {name:<25} | '{earnings_text}' -> ${earnings:.2f}")
                    passed += 1
                else:
                    print(f"❌ {name:<25} | '{earnings_text}' -> ${earnings:.2f} (expected ${expected:.2f})")
                    failed += 1
                    
            except ValueError as e:
                print(f"❌ {name:<25} | '{earnings_text}' -> Error: {e}")
                failed += 1
        else:
            print(f"❌ {name:<25} | '{earnings_text}' -> No match found")
            failed += 1
    
    print("-" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The earnings extraction logic is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. The earnings extraction logic needs further adjustment.")
        return False

def simulate_column_detection():
    """Simulate the column detection logic"""
    
    print("\n🔍 Testing Column Detection Logic")
    print("=" * 60)
    
    # Simulate table cells as they might appear in the OnlyMonster page
    # Based on the screenshot: Track Link | Cost | Earnings
    sample_rows = [
        {
            'name': 'X - naomifoxxxx',
            'cells': [
                'X - naomifoxxxx\nCreated 03-23-2025',  # Column 0: Name
                'Add Cost',                              # Column 1: Cost button
                '$ 630.40'                              # Column 2: Earnings
            ]
        },
        {
            'name': 'reddit-babycheeksx',
            'cells': [
                'reddit-babycheeksx\nCreated 12-01-2024',  # Column 0: Name
                'Add Cost',                                # Column 1: Cost button  
                '$ 2,361.50'                              # Column 2: Earnings
            ]
        }
    ]
    
    print("Simulating column detection (searching from right to left):")
    print("-" * 60)
    
    for row in sample_rows:
        name = row['name']
        cells = row['cells']
        
        print(f"\nProcessing: {name}")
        print(f"Cells found: {len(cells)}")
        
        for i, cell_text in enumerate(cells):
            print(f"  Column {i}: '{cell_text}'")
        
        # Simulate the earnings detection logic (search from right to left)
        earnings_found = False
        pattern = r'\$\s*[\d,]+(?:\.\d+)?'
        
        for i in range(len(cells) - 1, 0, -1):  # Search from right to left, skip name column
            earnings_text = cells[i].strip()
            earnings_match = re.search(pattern, earnings_text)
            
            if earnings_match:
                try:
                    earnings_str = earnings_match.group().replace('$', '').replace(',', '').replace(' ', '')
                    earnings = float(earnings_str)
                    print(f"  ✅ Found earnings ${earnings:.2f} in column {i}")
                    earnings_found = True
                    break
                except ValueError:
                    continue
        
        if not earnings_found:
            print(f"  ❌ No earnings found")
    
    print("\n🎉 Column detection simulation completed!")

if __name__ == "__main__":
    # Run the tests
    extraction_success = test_earnings_extraction()
    simulate_column_detection()
    
    if extraction_success:
        print("\n✅ The updated scraper logic should now correctly extract earnings!")
        print("\n💡 Next steps:")
        print("1. Run the OnlyMonster scraper to test with real data")
        print("2. Verify that earnings are properly captured and stored")
        print("3. Check that the analytics and reports show correct earnings data")
    else:
        print("\n❌ The earnings extraction logic needs further debugging.")
