#!/usr/bin/env python3
"""
Detailed analysis comparing 6/4/25 to 6/8/25 data
"""
import sqlite3
from datetime import datetime, timedelta
from analytics import TrackingAnalytics
from config import DATABASE_PATH, PRIORITY_LINKS

def get_detailed_comparison():
    """Get detailed comparison between 6/4 and 6/8 data"""
    
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()
        
        # Get 6/8 data (most recent)
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, timestamp
            FROM tracking_data t1
            WHERE timestamp = (
                SELECT MAX(timestamp) 
                FROM tracking_data t2 
                WHERE t2.tracking_link_name = t1.tracking_link_name
            )
            ORDER BY tracking_link_name
        ''')
        current_data = {name: (clicks, fans) for name, clicks, fans, _ in cursor.fetchall()}
        
        # Get 6/4 data (4 days ago)
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, timestamp
            FROM tracking_data
            WHERE timestamp < (SELECT MAX(timestamp) FROM tracking_data)
            ORDER BY tracking_link_name, timestamp DESC
        ''')
        
        previous_data = {}
        for name, clicks, fans, timestamp in cursor.fetchall():
            if name not in previous_data:
                previous_data[name] = (clicks, fans)
    
    return current_data, previous_data

def print_detailed_report():
    """Print detailed analysis report"""
    current_data, previous_data = get_detailed_comparison()
    
    print("="*80)
    print("📊 ONLYMONSTER TRACKING ANALYSIS: 6/4/25 → 6/8/25 (4 Days)")
    print("="*80)
    
    # Calculate totals
    total_new_clicks = 0
    total_new_fans = 0
    
    print("\n🎯 PRIORITY LINKS DETAILED PERFORMANCE:")
    print("-" * 60)
    
    for link in PRIORITY_LINKS:
        if link in current_data and link in previous_data:
            curr_clicks, curr_fans = current_data[link]
            prev_clicks, prev_fans = previous_data[link]
            
            click_change = curr_clicks - prev_clicks
            fan_change = curr_fans - prev_fans
            
            # Calculate conversion rates
            curr_rate = (curr_fans / curr_clicks * 100) if curr_clicks > 0 else 0
            prev_rate = (prev_fans / prev_clicks * 100) if prev_clicks > 0 else 0
            rate_change = curr_rate - prev_rate
            
            total_new_clicks += click_change
            total_new_fans += fan_change
            
            print(f"📈 {link}:")
            print(f"   📊 6/4: {prev_clicks:,} clicks, {prev_fans} fans ({prev_rate:.2f}%)")
            print(f"   📊 6/8: {curr_clicks:,} clicks, {curr_fans} fans ({curr_rate:.2f}%)")
            print(f"   🔥 Change: +{click_change} clicks, +{fan_change} fans ({rate_change:+.2f}%)")
            
            # Performance assessment
            if fan_change > 0:
                print(f"   ✅ GROWING: +{fan_change} new subscribers")
            elif fan_change == 0:
                print(f"   ⚠️  STAGNANT: No new subscribers")
            else:
                print(f"   ❌ DECLINING: {fan_change} subscribers")
            print()
    
    print(f"📈 TOTAL GROWTH (4 days): +{total_new_clicks:,} clicks, +{total_new_fans} fans")
    print(f"📊 Daily Average: +{total_new_clicks//4:,} clicks, +{total_new_fans//4} fans per day")
    
    # Top performers
    print("\n🏆 TOP PERFORMERS (6/4 → 6/8):")
    print("-" * 40)
    
    changes = []
    for link in current_data:
        if link in previous_data:
            curr_clicks, curr_fans = current_data[link]
            prev_clicks, prev_fans = previous_data[link]
            click_change = curr_clicks - prev_clicks
            fan_change = curr_fans - prev_fans
            changes.append((link, click_change, fan_change))
    
    # Sort by fan growth
    changes.sort(key=lambda x: x[2], reverse=True)
    
    print("👥 NEW SUBSCRIBERS:")
    for link, click_change, fan_change in changes[:5]:
        if fan_change > 0:
            print(f"   {link}: +{fan_change} fans (+{click_change} clicks)")
    
    print("\n🔗 NEW CLICKS:")
    changes.sort(key=lambda x: x[1], reverse=True)
    for link, click_change, fan_change in changes[:5]:
        if click_change > 0:
            print(f"   {link}: +{click_change} clicks")
    
    # Stagnant links
    print("\n⚠️  STAGNANT LINKS (No Growth):")
    print("-" * 30)
    stagnant = [link for link, click_change, fan_change in changes if click_change == 0 and fan_change == 0]
    if stagnant:
        for link in stagnant:
            print(f"   ❌ {link}: No activity")
    else:
        print("   ✅ All links showing activity!")
    
    # Conversion rate analysis
    print("\n📊 CONVERSION RATE CHANGES:")
    print("-" * 35)
    
    rate_changes = []
    for link in current_data:
        if link in previous_data:
            curr_clicks, curr_fans = current_data[link]
            prev_clicks, prev_fans = previous_data[link]
            
            curr_rate = (curr_fans / curr_clicks * 100) if curr_clicks > 0 else 0
            prev_rate = (prev_fans / prev_clicks * 100) if prev_clicks > 0 else 0
            rate_change = curr_rate - prev_rate
            
            rate_changes.append((link, curr_rate, rate_change))
    
    # Sort by current conversion rate
    rate_changes.sort(key=lambda x: x[1], reverse=True)
    
    print("🎯 CURRENT CONVERSION RATES:")
    for link, curr_rate, rate_change in rate_changes:
        if link in PRIORITY_LINKS:
            status = "📈" if rate_change > 0.01 else "📉" if rate_change < -0.01 else "➡️"
            print(f"   {status} {link}: {curr_rate:.2f}% ({rate_change:+.2f}%)")

def main():
    print("🔍 Running Detailed OnlyMonster Analysis")
    print_detailed_report()
    
    # Run AI analysis
    print("\n" + "="*80)
    print("🤖 AI STRATEGIC ANALYSIS")
    print("="*80)
    
    analytics = TrackingAnalytics()
    analysis = analytics.run_analysis(96)  # 4 days = 96 hours
    
    print(analysis['ai_analysis'])

if __name__ == "__main__":
    main()
