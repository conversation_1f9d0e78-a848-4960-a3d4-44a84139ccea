#!/usr/bin/env python3
"""
Daily OnlyMonster Report with Slack Integration
"""
import sqlite3
from datetime import datetime
from analytics import TrackingAnalytics
from slack_webhook import SlackWebhookNotifier
from link_combiner import <PERSON><PERSON><PERSON><PERSON>
from config import DAT<PERSON><PERSON>E_PATH, PRIORITY_LINKS, SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def generate_daily_report():
    """Generate focused daily report"""
    
    current_data, previous_data = get_comparison_data()
    
    print("🎯 ONLYMONSTER DAILY REPORT")
    print("📅 Period: 6/4/25 → 6/8/25 (4 days)")
    print("="*50)
    
    # Calculate key metrics
    total_new_fans = 0
    total_new_clicks = 0
    growing_sources = []
    stagnant_sources = []
    conversion_changes = []
    
    for link in PRIORITY_LINKS:
        if link in current_data and link in previous_data:
            curr_clicks, curr_fans = current_data[link]
            prev_clicks, prev_fans = previous_data[link]
            
            click_change = curr_clicks - prev_clicks
            fan_change = curr_fans - prev_fans
            
            total_new_clicks += click_change
            total_new_fans += fan_change
            
            # Track growing vs stagnant
            if fan_change > 0:
                growing_sources.append((link, fan_change, click_change))
            elif fan_change == 0 and click_change == 0:
                stagnant_sources.append(link)
            
            # Track conversion rate changes
            curr_rate = (curr_fans / curr_clicks * 100) if curr_clicks > 0 else 0
            prev_rate = (prev_fans / prev_clicks * 100) if prev_clicks > 0 else 0
            rate_change = curr_rate - prev_rate
            
            if abs(rate_change) > 0.05:  # Significant change threshold
                conversion_changes.append((link, curr_rate, rate_change))
    
    # 1. NEW SUBSCRIBERS SOURCE
    print("\n👥 NEW SUBSCRIBERS (Where growth came from):")
    growing_sources.sort(key=lambda x: x[1], reverse=True)
    for link, fans, clicks in growing_sources:
        print(f"   ✅ {link}: +{fans} fans (+{clicks} clicks)")
    
    if not growing_sources:
        print("   ❌ No new subscribers in this period")
    
    # 2. REVENUE SOURCES (assuming clicks = potential revenue)
    print(f"\n💰 TRAFFIC GROWTH (Latest activity):")
    growing_sources.sort(key=lambda x: x[2], reverse=True)
    for link, fans, clicks in growing_sources[:3]:
        print(f"   📈 {link}: +{clicks} clicks")
    
    # 3. STAGNANT LINKS
    print(f"\n⚠️  STAGNANT LINKS (No revenue or fans):")
    if stagnant_sources:
        for link in stagnant_sources:
            print(f"   ❌ {link}: No activity")
    else:
        print("   ✅ All priority links showing activity!")
    
    # 4. CONVERSION RATE CHANGES
    print(f"\n📊 CONVERSION RATE UPDATES:")
    if conversion_changes:
        for link, curr_rate, change in conversion_changes:
            direction = "📈" if change > 0 else "📉"
            print(f"   {direction} {link}: {curr_rate:.2f}% ({change:+.2f}%)")
    else:
        print("   ➡️  No significant conversion rate changes")
    
    # SUMMARY
    print(f"\n📈 SUMMARY:")
    print(f"   Total Growth: +{total_new_fans} fans, +{total_new_clicks:,} clicks")
    print(f"   Daily Average: +{total_new_fans//4} fans, +{total_new_clicks//4:,} clicks")
    print(f"   Active Links: {len(growing_sources)}/{len(PRIORITY_LINKS)}")
    
    return {
        'total_new_fans': total_new_fans,
        'total_new_clicks': total_new_clicks,
        'growing_sources': growing_sources,
        'stagnant_sources': stagnant_sources
    }

def get_comparison_data():
    """Get current vs previous data with link combinations"""
    link_combiner = LinkCombiner()
    return link_combiner.get_combined_comparison_data()

def get_ai_insights():
    """Get AI analysis"""
    analytics = TrackingAnalytics()
    analysis = analytics.run_analysis(96)
    return analysis['ai_analysis']

def send_to_slack(report_data, ai_insights):
    """Send report to Slack"""
    print(f"\n📤 SENDING TO SLACK...")
    print("-" * 25)
    
    try:
        slack_notifier = SlackWebhookNotifier()
        message = slack_notifier.format_slack_message(report_data, ai_insights)
        
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            print("✅ Report sent to Slack successfully!")
            print(f"📱 Check your channel: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
        else:
            print("❌ Failed to send to Slack")
            print("📋 Message prepared but not sent due to permissions")
            
        return success
        
    except Exception as e:
        print(f"❌ Slack error: {e}")
        return False

def main():
    # Generate report
    report_data = generate_daily_report()
    
    # Get AI insights
    print(f"\n🤖 AI INSIGHTS & RECOMMENDATIONS:")
    print("-" * 40)
    ai_insights = get_ai_insights()
    
    # Extract key recommendations from AI
    lines = ai_insights.split('\n')
    in_recommendations = False
    
    for line in lines:
        if 'Strategic Recommendations' in line or 'Action Items' in line:
            in_recommendations = True
            continue
        elif in_recommendations and line.strip():
            if line.startswith('- ') or line.startswith('1.') or line.startswith('2.'):
                print(f"   💡 {line.strip()}")
            elif line.strip() and not line.startswith(' '):
                break
    
    # Send to Slack
    slack_success = send_to_slack(report_data, ai_insights)
    
    if not slack_success:
        print(f"\n📧 SLACK SETUP INSTRUCTIONS:")
        print("To enable Slack notifications:")
        print("1. Go to https://api.slack.com/apps")
        print("2. Select your OnlyMonster app")
        print("3. Go to 'OAuth & Permissions'")
        print("4. Add these scopes: 'chat:write', 'channels:read'")
        print("5. Reinstall app to workspace")
        print("6. Invite bot to your channel: /invite @your-bot-name")
        print(f"7. Channel ID: {SLACK_CHANNEL_ID}")

if __name__ == "__main__":
    main()
