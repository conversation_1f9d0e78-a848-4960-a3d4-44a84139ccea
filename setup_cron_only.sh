#!/bin/bash

# Simple Cron Job Setup for OnlyMonster Daily Reports
# Alternative to systemd service

echo "⏰ Setting up Cron Job for OnlyMonster Daily Reports"
echo "===================================================="

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Project directory: $SCRIPT_DIR"

# Make the scheduler executable
chmod +x "$SCRIPT_DIR/daily_scheduler.py"

# Create a simple daily runner script
cat > "$SCRIPT_DIR/run_daily_report.sh" << EOF
#!/bin/bash
cd "$SCRIPT_DIR"
/usr/bin/python3 "$SCRIPT_DIR/standardized_daily_report.py" >> "$SCRIPT_DIR/daily_report.log" 2>&1
EOF

chmod +x "$SCRIPT_DIR/run_daily_report.sh"
echo "✅ Created run_daily_report.sh"

# Add cron job
echo "📅 Adding cron job..."

# Create cron job entry
CRON_JOB="0 6 * * * $SCRIPT_DIR/run_daily_report.sh"

# Add to crontab
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "✅ Cron job added successfully!"
echo ""
echo "📋 Cron Job Details:"
echo "  • Time: 6:00 AM daily"
echo "  • Command: $SCRIPT_DIR/run_daily_report.sh"
echo "  • Log file: $SCRIPT_DIR/daily_report.log"
echo ""
echo "🔧 Management Commands:"
echo "  • View cron jobs:    crontab -l"
echo "  • Edit cron jobs:    crontab -e"
echo "  • View logs:         tail -f $SCRIPT_DIR/daily_report.log"
echo ""
echo "✅ Setup complete! Daily reports will run at 6:00 AM automatically."

# Show current crontab
echo ""
echo "📋 Current crontab:"
crontab -l
