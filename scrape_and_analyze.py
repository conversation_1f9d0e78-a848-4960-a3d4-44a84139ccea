#!/usr/bin/env python3
"""
Run a fresh scrape and immediate analysis
"""
from onlymonster_scraper import OnlyMonsterScraper
from analytics import TrackingAnalytics
from standardized_daily_report import generate_standardized_report, format_slack_message
from slack_webhook import SlackWebhookNotifier
from config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

def main():
    print("🚀 Starting OnlyMonster Scrape & Analysis")
    print("="*60)
    
    # Run scraper
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        
        if data:
            print(f"\n✅ Successfully scraped {len(data)} tracking links")
            
            # Run standardized analytics
            print("\n� Running Standardized Analysis...")
            report_data = generate_standardized_report()

            # Send to Slack
            print(f"\n📤 SENDING TO SLACK...")
            print("-" * 25)

            try:
                slack_notifier = SlackWebhookNotifier()
                message = format_slack_message(report_data)

                success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)

                if success:
                    print("✅ Fresh data + standardized report sent to Slack!")
                    print(f"📱 Check: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
                else:
                    print("❌ Failed to send to Slack")

            except Exception as e:
                print(f"❌ Slack error: {e}")
                print("📧 Report generated locally only")
        else:
            print("❌ No data collected")
            
    except Exception as e:
        print(f"❌ Scraping failed: {e}")

if __name__ == "__main__":
    main()
