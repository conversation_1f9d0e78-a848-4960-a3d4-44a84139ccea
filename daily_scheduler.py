#!/usr/bin/env python3
"""
Daily OnlyMonster Report Scheduler
Runs the standardized daily report automatically at a specified time

⚠️  DEPRECATED: This scheduler is no longer used.
    The daily report is now handled by SystemD timer: onlymonster-daily.timer
    Do not run this script to avoid duplicate notifications.
"""
import schedule
import time
import logging
import sys
import os
from datetime import datetime
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_dir))

from standardized_daily_report import generate_standardized_report, format_slack_message
from slack_webhook import SlackWebhookNotifier
from config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID

# Setup logging
log_file = project_dir / "daily_scheduler.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

def run_daily_report():
    """Run the daily report with error handling"""
    try:
        logging.info("🚀 Starting scheduled daily OnlyMonster report...")
        
        # Generate standardized report
        report_data = generate_standardized_report()
        
        # Send to Slack
        logging.info("📤 Sending report to Slack...")
        
        slack_notifier = SlackWebhookNotifier()
        message = format_slack_message(report_data)
        
        success = slack_notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        
        if success:
            logging.info("✅ Daily report sent to Slack successfully!")
            logging.info(f"📱 Channel: https://cheeksglobal.slack.com/archives/{SLACK_CHANNEL_ID}")
        else:
            logging.error("❌ Failed to send report to Slack")
            
    except Exception as e:
        logging.error(f"❌ Daily report failed: {e}")
        
        # Try to send error notification to Slack
        try:
            error_message = f"🚨 *OnlyMonster Daily Report Failed*\n\nError: {str(e)}\nTime: {datetime.now().strftime('%H:%M %m/%d/%y')}"
            slack_notifier = SlackWebhookNotifier()
            slack_notifier.send_message_via_api(error_message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
        except:
            pass  # Don't fail on error notification failure

def main():
    """Main scheduler function"""
    print("🕐 OnlyMonster Daily Report Scheduler Started")
    print(f"📁 Working directory: {project_dir}")
    print("⏰ Scheduled to run daily at 9:00 AM")
    print("Press Ctrl+C to stop")
    
    # Schedule the daily report
    schedule.every().day.at("09:00").do(run_daily_report)
    
    # Also schedule for testing - run every hour during business hours
    # Uncomment the line below for testing
    # schedule.every().hour.do(run_daily_report)
    
    logging.info("Daily scheduler initialized - reports will run at 9:00 AM daily")
    
    # Keep the scheduler running
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            print("\n🛑 Scheduler stopped by user")
            logging.info("Scheduler stopped by user")
            break
        except Exception as e:
            logging.error(f"Scheduler error: {e}")
            time.sleep(60)

if __name__ == "__main__":
    # Change to project directory
    os.chdir(project_dir)
    main()
