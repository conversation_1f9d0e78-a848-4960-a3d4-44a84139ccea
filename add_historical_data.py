#!/usr/bin/env python3
"""
Add historical data for OnlyMonster tracking links.
This script adds data for 3/28, 4/12, 5/17, 5/29, and 6/4 in chronological order.
"""
import sqlite3
from datetime import datetime
from config import DATABASE_PATH

def add_historical_data():
    """Add historical data in chronological order"""
    print("📊 Adding historical OnlyMonster data...")
    
    # Historical data organized by date
    # Format: (link_name, fans, clicks, earnings, timestamp)
    historical_data = [
        # 3/28/2025 data
        ('reddit-babycheeksx', 117, 3688, None, '2025-03-28 12:00:00'),
        ('reels-naominoface', 32, 2717, None, '2025-03-28 12:00:00'),
        ('reels-lilfoxnaomi-aug-22', 141, 18434, None, '2025-03-28 12:00:00'),
        ('chive-nyla-aug-8', 15, 3750, None, '2025-03-28 12:00:00'),
        ('tiktok-aug-1-24', 119, 7610, None, '2025-03-28 12:00:00'),
        ('chive-aug-1-24', 394, 40118, None, '2025-03-28 12:00:00'),
        ('Reels2024', 921, 84207, None, '2025-03-28 12:00:00'),
        
        # 4/12/2025 data
        ('reddit-babycheeksx', 120, 3891, None, '2025-04-12 12:00:00'),
        ('reels-naominoface', 50, 3719, None, '2025-04-12 12:00:00'),
        ('reels-lilfoxnaomi-aug-22', 147, 19040, None, '2025-04-12 12:00:00'),
        ('chive-nyla-aug-8', 15, 3752, None, '2025-04-12 12:00:00'),
        ('tiktok-aug-1-24', 121, 7777, None, '2025-04-12 12:00:00'),
        ('chive-aug-1-24', 411, 41825, None, '2025-04-12 12:00:00'),
        ('Reels2024', 1041, 90765, None, '2025-04-12 12:00:00'),
        
        # 5/17/2025 data
        ('reddit-babycheeksx', 125, 4209, None, '2025-05-17 12:00:00'),
        ('reels-naominoface', 98, 5386, None, '2025-05-17 12:00:00'),
        ('reels-lilfoxnaomi-aug-22', 156, 20119, None, '2025-05-17 12:00:00'),
        ('chive-nyla-aug-8', 16, 3760, None, '2025-05-17 12:00:00'),
        ('tiktok-aug-1-24', 132, 8035, None, '2025-05-17 12:00:00'),
        ('chive-aug-1-24', 443, 45354, None, '2025-05-17 12:00:00'),
        ('Reels2024', 1207, 99059, None, '2025-05-17 12:00:00'),
        
        # 5/29/2025 data
        ('reddit-babycheeksx', 127, 4343, None, '2025-05-29 12:00:00'),
        ('reels-naominoface', 110, 6914, None, '2025-05-29 12:00:00'),
        ('reels-lilfoxnaomi-aug-22', 156, 20390, None, '2025-05-29 12:00:00'),
        ('chive-nyla-aug-8', 16, 3762, None, '2025-05-29 12:00:00'),
        ('tiktok-aug-1-24', 134, 8074, None, '2025-05-29 12:00:00'),
        ('chive-aug-1-24', 455, 47771, None, '2025-05-29 12:00:00'),
        ('Reels2024', 1231, 100512, None, '2025-05-29 12:00:00'),
        
        # 6/4/2025 data
        ('reddit-babycheeksx', 130, 4442, None, '2025-06-04 12:00:00'),
        ('reels-naominoface', 122, 5983, None, '2025-06-04 12:00:00'),
        ('reels-lilfoxnaomi-aug-22', 158, 20540, None, '2025-06-04 12:00:00'),
        ('chive-nyla-aug-8', 16, 3764, None, '2025-06-04 12:00:00'),
        ('tiktok-aug-1-24', 134, 8105, None, '2025-06-04 12:00:00'),
        ('chive-aug-1-24', 466, 48720, None, '2025-06-04 12:00:00'),
        ('Reels2024', 1246, 101828, None, '2025-06-04 12:00:00'),
    ]
    
    # Convert None earnings to 0.00
    processed_data = []
    for link_name, fans, clicks, earnings, timestamp in historical_data:
        earnings_value = 0.00 if earnings is None else earnings
        processed_data.append((link_name, clicks, fans, earnings_value, timestamp))
    
    print(f"📝 Prepared {len(processed_data)} historical records")
    
    # Now we need to insert this data in the correct chronological position
    # Strategy: Extract all data, merge with new data, sort, and re-insert
    
    with sqlite3.connect(DATABASE_PATH) as conn:
        cursor = conn.cursor()
        
        # Get all existing data
        cursor.execute('''
            SELECT tracking_link_name, clicks, fans, earnings, timestamp
            FROM tracking_data
            ORDER BY timestamp ASC
        ''')
        existing_data = cursor.fetchall()
        print(f"📊 Found {len(existing_data)} existing records")
        
        # Combine existing and new data
        all_data = list(existing_data) + processed_data
        
        # Sort by timestamp to ensure chronological order
        all_data.sort(key=lambda x: x[4])  # Sort by timestamp (index 4)
        
        print(f"🔄 Combined and sorted {len(all_data)} total records")
        
        # Clear the table
        cursor.execute('DELETE FROM tracking_data')
        print("🗑️  Cleared existing data")
        
        # Re-insert all data in chronological order
        cursor.executemany('''
            INSERT INTO tracking_data (tracking_link_name, clicks, fans, earnings, timestamp)
            VALUES (?, ?, ?, ?, ?)
        ''', all_data)
        
        conn.commit()
        print(f"✅ Inserted {len(all_data)} records in chronological order")
        
        # Verify the result
        cursor.execute('''
            SELECT DATE(timestamp) as date, COUNT(*) as count, MIN(id) as min_id, MAX(id) as max_id
            FROM tracking_data
            GROUP BY DATE(timestamp)
            ORDER BY date ASC
        ''')
        
        dates = cursor.fetchall()
        print(f'\n📅 Final database structure:')
        print('Date       | Count | ID Range')
        print('-' * 35)
        for date, count, min_id, max_id in dates:
            print(f'{date} | {count:>5} | {min_id}-{max_id}')
        
        # Verify latest timestamp matches latest entry
        cursor.execute('SELECT MAX(timestamp) FROM tracking_data')
        latest_timestamp = cursor.fetchone()[0]
        
        cursor.execute('SELECT timestamp FROM tracking_data ORDER BY id DESC LIMIT 1')
        latest_entry = cursor.fetchone()[0]
        
        print(f'\n🔍 Data ordering verification:')
        print(f'Latest timestamp: {latest_timestamp}')
        print(f'Latest entry: {latest_entry}')
        print(f'Match: {latest_timestamp == latest_entry} ✅' if latest_timestamp == latest_entry else f'Match: {latest_timestamp == latest_entry} ❌')

def main():
    """Main function"""
    print("🚀 Starting historical data import...")
    add_historical_data()
    print("\n🎯 Historical data import complete!")
    print("📊 Your database now contains the complete historical timeline.")
    print("🔄 Run the daily report to see the updated analysis.")

if __name__ == "__main__":
    main()
