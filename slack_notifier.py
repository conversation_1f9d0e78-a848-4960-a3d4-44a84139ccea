"""
Slack notification module for OnlyMonster analytics
"""
import json
from datetime import datetime
from slack_sdk import <PERSON><PERSON><PERSON>
from slack_sdk.errors import SlackApiError
from config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID, PRIORITY_LINKS


class SlackNotifier:
    def __init__(self):
        self.client = WebClient(token=SLACK_BOT_TOKEN)
        self.channel_id = SLACK_CHANNEL_ID
    
    def test_connection(self):
        """Test Slack connection"""
        try:
            response = self.client.auth_test()
            print(f"✅ Slack connection successful! Bot: {response['user']}")
            return True
        except SlackApiError as e:
            print(f"❌ Slack connection failed: {e.response['error']}")
            return False
    
    def format_daily_report_message(self, report_data, ai_insights=""):
        """Format the daily report for Slack"""
        
        # Header
        message = "🎯 *OnlyMonster Daily Report*\n"
        message += f"📅 *Period:* 6/4/25 → 6/8/25 (4 days)\n"
        message += "=" * 40 + "\n\n"
        
        # Summary stats
        total_fans = report_data.get('total_new_fans', 0)
        total_clicks = report_data.get('total_new_clicks', 0)
        
        message += f"📈 *SUMMARY:*\n"
        message += f"• Total Growth: +{total_fans} fans, +{total_clicks:,} clicks\n"
        message += f"• Daily Average: +{total_fans//4} fans, +{total_clicks//4:,} clicks\n\n"
        
        # New subscribers
        growing_sources = report_data.get('growing_sources', [])
        if growing_sources:
            message += "👥 *NEW SUBSCRIBERS:*\n"
            for link, fans, clicks in growing_sources[:5]:  # Top 5
                message += f"• {link}: +{fans} fans (+{clicks} clicks)\n"
            message += "\n"
        
        # Top traffic sources
        if growing_sources:
            message += "💰 *TOP TRAFFIC SOURCES:*\n"
            sorted_by_clicks = sorted(growing_sources, key=lambda x: x[2], reverse=True)
            for link, fans, clicks in sorted_by_clicks[:3]:  # Top 3
                message += f"• {link}: +{clicks} clicks\n"
            message += "\n"
        
        # Stagnant links
        stagnant_sources = report_data.get('stagnant_sources', [])
        if stagnant_sources:
            message += "⚠️ *STAGNANT LINKS:*\n"
            for link in stagnant_sources:
                message += f"• {link}: No activity\n"
            message += "\n"
        else:
            message += "✅ *All priority links showing activity!*\n\n"
        
        # AI insights (simplified)
        if ai_insights:
            message += "🤖 *KEY RECOMMENDATIONS:*\n"
            # Extract key points from AI insights
            lines = ai_insights.split('\n')
            recommendations = []
            
            for line in lines:
                if any(keyword in line.lower() for keyword in ['scale', 'optimize', 'investigate', 'consider']):
                    clean_line = line.strip().replace('- ', '').replace('*', '')
                    if clean_line and len(clean_line) < 100:
                        recommendations.append(clean_line)
            
            for rec in recommendations[:3]:  # Top 3 recommendations
                message += f"• {rec}\n"
        
        message += f"\n🕐 *Report generated:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return message
    
    def send_daily_report(self, report_data, ai_insights=""):
        """Send daily report to Slack"""
        try:
            message = self.format_daily_report_message(report_data, ai_insights)
            
            response = self.client.chat_postMessage(
                channel=self.channel_id,
                text=message,
                mrkdwn=True
            )
            
            print(f"✅ Daily report sent to Slack successfully!")
            return True
            
        except SlackApiError as e:
            print(f"❌ Failed to send Slack message: {e.response['error']}")
            return False
    
    def send_alert(self, title, message, urgency="info"):
        """Send alert message to Slack"""
        try:
            # Choose emoji based on urgency
            emoji_map = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "success": "✅"
            }
            
            emoji = emoji_map.get(urgency, "ℹ️")
            
            formatted_message = f"{emoji} *{title}*\n{message}"
            
            response = self.client.chat_postMessage(
                channel=self.channel_id,
                text=formatted_message,
                mrkdwn=True
            )
            
            print(f"✅ Alert sent to Slack successfully!")
            return True
            
        except SlackApiError as e:
            print(f"❌ Failed to send Slack alert: {e.response['error']}")
            return False
    
    def send_performance_summary(self, current_data, previous_data):
        """Send performance summary with key metrics"""
        try:
            message = "📊 *OnlyMonster Performance Summary*\n\n"
            
            # Calculate top performers
            changes = []
            for link in PRIORITY_LINKS:
                if link in current_data and link in previous_data:
                    curr_clicks, curr_fans = current_data[link]
                    prev_clicks, prev_fans = previous_data[link]
                    
                    click_change = curr_clicks - prev_clicks
                    fan_change = curr_fans - prev_fans
                    
                    # Calculate conversion rate
                    curr_rate = (curr_fans / curr_clicks * 100) if curr_clicks > 0 else 0
                    
                    changes.append((link, click_change, fan_change, curr_rate, curr_clicks, curr_fans))
            
            # Sort by fan growth
            changes.sort(key=lambda x: x[2], reverse=True)
            
            message += "*🏆 TOP PERFORMERS:*\n"
            for link, click_change, fan_change, rate, total_clicks, total_fans in changes[:3]:
                message += f"• {link}: +{fan_change} fans, {rate:.1f}% conversion\n"
            
            message += f"\n*📈 TOTAL METRICS:*\n"
            total_clicks = sum(data[4] for data in changes)
            total_fans = sum(data[5] for data in changes)
            message += f"• Total: {total_clicks:,} clicks, {total_fans} fans\n"
            
            response = self.client.chat_postMessage(
                channel=self.channel_id,
                text=message,
                mrkdwn=True
            )
            
            print(f"✅ Performance summary sent to Slack!")
            return True
            
        except SlackApiError as e:
            print(f"❌ Failed to send performance summary: {e.response['error']}")
            return False


def test_slack_integration():
    """Test Slack integration"""
    print("🔍 Testing Slack integration...")
    
    notifier = SlackNotifier()
    
    # Test connection
    if notifier.test_connection():
        # Send test message
        test_data = {
            'total_new_fans': 15,
            'total_new_clicks': 978,
            'growing_sources': [
                ('Reels2024', 4, 272),
                ('reels-naominoface', 3, 167),
                ('reddit-babycheeksx', 2, 78)
            ],
            'stagnant_sources': []
        }
        
        notifier.send_daily_report(test_data, "Test AI insights for Slack integration")
        return True
    
    return False


if __name__ == "__main__":
    test_slack_integration()
