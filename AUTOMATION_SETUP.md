# OnlyMonster Daily Report Automation Setup

This guide will help you set up automatic daily execution of your OnlyMonster reports that **continues running even after SSH disconnection**.

## 🚀 Quick Setup (Recommended)

### Option 1: Systemd Timer (Best for servers - SSH-safe)

Run the automated setup script:

```bash
./setup_daily_automation.sh
```

This will:
- ✅ Set up a systemd timer that runs in the background
- ✅ **Continues running after SSH disconnection**
- ✅ Automatically restart if it crashes
- ✅ Start on system boot
- ✅ Send daily reports at 6:00 AM
- ✅ Include error notifications to Slack
- ✅ More reliable than cron for server environments

### Option 2: Simple Cron Job

If you prefer a simpler cron-based approach:

```bash
./setup_cron_only.sh
```

This will:
- ✅ Add a cron job to run daily at 9:00 AM
- ✅ Create log files for monitoring
- ✅ Simpler setup, less robust than systemd

## 📅 Schedule Details

**Default Schedule:** 9:00 AM daily

**What happens:**
1. Script automatically scrapes latest OnlyMonster data
2. Generates standardized analytics report
3. Includes AI improvement suggestions
4. Sends formatted report to your Slack channel
5. Logs all activity for monitoring

## 🔧 Management Commands

### Easy Management Script

```bash
# Check status
./manage_automation.sh status

# Run a test report now
./manage_automation.sh test

# View recent logs
./manage_automation.sh logs

# View live logs
./manage_automation.sh logs-live

# Stop/start automation
./manage_automation.sh stop
./manage_automation.sh start
```

### Manual Systemd Commands

```bash
# Check if timer is running
sudo systemctl status onlymonster-daily.timer

# View live logs
sudo journalctl -u onlymonster-daily -f

# Stop the timer
sudo systemctl stop onlymonster-daily.timer

# Start the timer
sudo systemctl start onlymonster-daily.timer

# Run report immediately
sudo systemctl start onlymonster-daily.service

# Disable automatic startup
sudo systemctl disable onlymonster-daily.timer
```

### Cron Job Management

```bash
# View current cron jobs
crontab -l

# Edit cron jobs
crontab -e

# View logs
tail -f daily_report.log

# Remove the cron job
crontab -e  # Then delete the OnlyMonster line
```

## 🕐 Changing the Schedule

### For Systemd Service:

1. Edit the scheduler file:
```bash
nano daily_scheduler.py
```

2. Change this line:
```python
schedule.every().day.at("09:00").do(run_daily_report)
```

3. Restart the service:
```bash
sudo systemctl restart onlymonster-daily
```

### For Cron Job:

1. Edit crontab:
```bash
crontab -e
```

2. Modify the time (format: minute hour day month weekday):
```bash
# Examples:
0 8 * * *   # 8:00 AM daily
30 18 * * * # 6:30 PM daily
0 9 * * 1-5 # 9:00 AM weekdays only
```

## 📊 Monitoring

### Check if automation is working:

1. **View logs:**
```bash
# Systemd
sudo journalctl -u onlymonster-daily -n 50

# Cron
tail -f daily_report.log
```

2. **Check Slack channel** for daily reports

3. **Manual test run:**
```bash
python3 standardized_daily_report.py
```

## 🚨 Troubleshooting

### Service not starting:
```bash
# Check service status
sudo systemctl status onlymonster-daily

# View detailed logs
sudo journalctl -u onlymonster-daily -n 100
```

### Reports not sending to Slack:
1. Check Slack credentials in `config.py`
2. Verify bot token has correct permissions
3. Test manually: `python3 standardized_daily_report.py`

### Cron job not running:
```bash
# Check if cron service is running
sudo systemctl status cron

# Check cron logs
grep CRON /var/log/syslog
```

## 🔄 Updates and Maintenance

### To update the report format:
1. Edit `standardized_daily_report.py`
2. Restart the service: `sudo systemctl restart onlymonster-daily`

### To change Slack settings:
1. Edit `config.py`
2. Restart the automation

### To backup your setup:
```bash
# Backup the entire project
tar -czf onlymonster-backup-$(date +%Y%m%d).tar.gz /root/onlymonster-automations/
```

## 📱 What You'll Receive

Every day at 9:00 AM, you'll get a Slack message with:

- **NEW SUBSCRIBERS** - Where growth came from
- **TRAFFIC GROWTH** - Top performing links  
- **CONVERSION RATE CHANGES** - Rate improvements/declines
- **PERFORMANCE vs AVERAGE** - Deviation analysis
- **AI IMPROVEMENT SUGGESTIONS** - Actionable recommendations
- **SUMMARY** - Total metrics and daily averages

## ✅ Verification

After setup, verify everything is working:

1. ✅ Service is running: `sudo systemctl status onlymonster-daily`
2. ✅ Logs show no errors: `sudo journalctl -u onlymonster-daily -f`
3. ✅ Test run works: `python3 standardized_daily_report.py`
4. ✅ Slack receives the message
5. ✅ Schedule is correct for your timezone

Your OnlyMonster reports will now run automatically every day! 🎉
