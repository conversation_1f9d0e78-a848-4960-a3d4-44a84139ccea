# Fix Slack Integration - Step by Step Guide

## 🔍 **Issue Identified:**
Your Slack app tokens are valid, but the app is missing required scopes:
- **Current scopes:** `identify`, `app_configurations:read`, `app_configurations:write`
- **Needed scopes:** `chat:write`, `channels:read`, `groups:read`, `mpim:read`, `im:read`

## 🛠️ **Step-by-Step Fix:**

### 1. **Add Required Scopes**
1. Go to https://api.slack.com/apps
2. Select your "updates" app (App ID: A0917GCNQ1W)
3. Click "OAuth & Permissions" in the left sidebar
4. Scroll down to "Scopes" section
5. Under "Bot Token Scopes", click "Add an OAuth Scope"
6. Add these scopes:
   - `chat:write` (Send messages as the app)
   - `channels:read` (View basic information about public channels)
   - `groups:read` (View basic information about private channels)
   - `mpim:read` (View basic information about group DMs)
   - `im:read` (View basic information about direct messages)

### 2. **Reinstall App to Workspace**
1. After adding scopes, you'll see a yellow banner saying "Please reinstall your app"
2. Click "Reinstall App" button
3. Review the permissions and click "Allow"
4. **IMPORTANT:** Copy the new Bot User OAuth Token (starts with `xoxb-`)

### 3. **Invite Bot to Channel**
1. Go to your Slack channel: https://cheeksglobal.slack.com/archives/C090FEVA18D
2. Type: `/invite @updates` (or whatever your bot name is)
3. The bot should now appear in the channel member list

### 4. **Update Token in Code**
Replace the bot token in `config.py` with the new `xoxb-` token from step 2.

## 🧪 **Test the Fix:**
Run the debug script again:
```bash
python3 debug_slack.py
```

All tests should now pass ✅

## 🚀 **Alternative: Webhook Solution**
If the OAuth approach is too complex, I can set up a webhook-based solution:

1. In your Slack app, go to "Incoming Webhooks"
2. Activate incoming webhooks
3. Click "Add New Webhook to Workspace"
4. Select your channel
5. Copy the webhook URL
6. Use this URL for direct message posting (simpler, no scopes needed)

Let me know which approach you prefer!
