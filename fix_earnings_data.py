#!/usr/bin/env python3
"""
Scrip<PERSON> to fix OnlyMonster earnings data issues:
1. Delete problematic rows (29-73, which are IDs 35-91)
2. Test the updated scraper to ensure earnings are captured correctly
"""

from database import TrackingDatabase
import sys

def main():
    print("🔧 OnlyMonster Earnings Data Fix")
    print("=" * 50)
    
    db = TrackingDatabase()
    
    # Step 1: Show current data status
    print("\n📊 Current Database Status:")
    all_data = db.get_all_data_with_ids()
    print(f"Total records: {len(all_data)}")
    
    # Show some sample records to confirm the issue
    print("\nSample of most recent records (should have 0 earnings - this is the bug):")
    for i, record in enumerate(all_data[:5], 1):
        print(f"  {i}. ID={record[0]}: {record[1]} - Earnings: ${record[4]:.2f}")
    
    print("\nSample of older records (some should have earnings data):")
    for i, record in enumerate(all_data[30:35], 31):
        print(f"  {i}. ID={record[0]}: {record[1]} - Earnings: ${record[4]:.2f}")
    
    # Step 2: Identify problematic rows
    print(f"\n🎯 Identifying problematic rows to delete:")
    print(f"Row 29 (ID {all_data[28][0]}): {all_data[28][1]} - {all_data[28][5]}")
    print(f"Row 73 (ID {all_data[72][0]}): {all_data[72][1]} - {all_data[72][5]}")
    print(f"Will delete IDs {all_data[72][0]} to {all_data[28][0]} (inclusive)")
    
    # Step 3: Confirm deletion
    response = input(f"\n⚠️  Are you sure you want to delete rows 29-73 (IDs {all_data[72][0]}-{all_data[28][0]})? [y/N]: ")
    if response.lower() != 'y':
        print("❌ Operation cancelled.")
        return
    
    # Step 4: Delete problematic rows
    print(f"\n🗑️  Deleting problematic rows...")
    start_id = all_data[72][0]  # Row 73 (ID 35)
    end_id = all_data[28][0]    # Row 29 (ID 91)
    
    deleted_count = db.delete_rows_by_id_range(start_id, end_id)
    print(f"✅ Successfully deleted {deleted_count} rows")
    
    # Step 5: Show updated status
    print(f"\n📊 Updated Database Status:")
    updated_data = db.get_all_data_with_ids()
    print(f"Total records: {len(updated_data)} (was {len(all_data)})")
    print(f"Records removed: {len(all_data) - len(updated_data)}")
    
    print("\n✅ Database cleanup completed!")
    print("\n💡 Next steps:")
    print("1. Test the updated scraper to ensure earnings are captured correctly")
    print("2. Run a new scrape to get fresh data with proper earnings")
    print("3. Verify that the earnings column is now working properly")

if __name__ == "__main__":
    main()
