#!/usr/bin/env python3
"""
Simple Slack webhook integration (easier setup)
"""
import requests
import json
from datetime import datetime

class SimpleSlackNotifier:
    def __init__(self, webhook_url=None):
        # You'll get this URL from Slack's "Incoming Webhooks" feature
        self.webhook_url = webhook_url
    
    def send_webhook_message(self, message):
        """Send message via webhook (no authentication needed)"""
        if not self.webhook_url:
            print("❌ No webhook URL configured")
            return False
        
        payload = {
            "text": message,
            "mrkdwn": True,
            "username": "OnlyMonster Bot",
            "icon_emoji": ":chart_with_upwards_trend:"
        }
        
        try:
            response = requests.post(self.webhook_url, json=payload)
            
            if response.status_code == 200:
                print("✅ Message sent via webhook successfully!")
                return True
            else:
                print(f"❌ Webhook failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Webhook request failed: {e}")
            return False
    
    def format_onlymonster_report(self, report_data):
        """Format OnlyMonster report for Slack"""
        message = ":chart_with_upwards_trend: *OnlyMonster Daily Report*\n"
        message += f":calendar: 6/4/25 → 6/8/25 (4 days)\n"
        message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
        
        # Summary
        total_fans = report_data.get('total_new_fans', 0)
        total_clicks = report_data.get('total_new_clicks', 0)
        
        message += f":rocket: *GROWTH SUMMARY*\n"
        message += f"• +{total_fans} new fans\n"
        message += f"• +{total_clicks:,} new clicks\n"
        message += f"• {total_fans//4} fans/day average\n\n"
        
        # Top performers
        growing_sources = report_data.get('growing_sources', [])
        if growing_sources:
            message += ":trophy: *TOP PERFORMERS*\n"
            for link, fans, clicks in growing_sources[:3]:
                message += f"• {link}: +{fans} fans (+{clicks} clicks)\n"
            message += "\n"
        
        # Stagnant links
        stagnant_sources = report_data.get('stagnant_sources', [])
        if stagnant_sources:
            message += ":warning: *NEEDS ATTENTION*\n"
            for link in stagnant_sources:
                message += f"• {link}: No growth\n"
            message += "\n"
        else:
            message += ":white_check_mark: All priority links showing activity!\n\n"
        
        # Quick recommendations
        message += ":robot_face: *QUICK INSIGHTS*\n"
        if growing_sources:
            top_performer = growing_sources[0][0]
            message += f"• Scale {top_performer} strategy\n"
        message += "• Monitor conversion rates\n"
        message += "• Focus on high-performing sources\n\n"
        
        message += f":clock3: {datetime.now().strftime('%H:%M %m/%d/%y')}"
        
        return message

def setup_webhook_instructions():
    """Print instructions for setting up webhook"""
    print("🔧 SLACK WEBHOOK SETUP INSTRUCTIONS:")
    print("="*50)
    print("1. Go to https://api.slack.com/apps")
    print("2. Select your 'updates' app")
    print("3. Click 'Incoming Webhooks' in the left sidebar")
    print("4. Toggle 'Activate Incoming Webhooks' to ON")
    print("5. Click 'Add New Webhook to Workspace'")
    print("6. Select your channel (analytics or similar)")
    print("7. Click 'Allow'")
    print("8. Copy the webhook URL (starts with https://hooks.slack.com/...)")
    print("9. Update the webhook_url in this script")
    print("\n✅ Webhook is much simpler - no scopes or tokens needed!")

def test_webhook_with_sample_url():
    """Test webhook format without actually sending"""
    print("📝 WEBHOOK MESSAGE PREVIEW:")
    print("="*40)
    
    # Sample data
    sample_data = {
        'total_new_fans': 15,
        'total_new_clicks': 978,
        'growing_sources': [
            ('Reels2024', 4, 272),
            ('reels-naominoface', 3, 167),
            ('reddit-babycheeksx', 2, 78)
        ],
        'stagnant_sources': []
    }
    
    notifier = SimpleSlackNotifier()
    message = notifier.format_onlymonster_report(sample_data)
    
    print(message)
    print("="*40)
    
    print("\n💡 To send this message:")
    print("1. Set up webhook URL (see instructions above)")
    print("2. Update webhook_url in the script")
    print("3. Run: notifier.send_webhook_message(message)")

def main():
    """Main function"""
    print("🚀 SIMPLE SLACK WEBHOOK INTEGRATION")
    print("="*45)
    
    # Show setup instructions
    setup_webhook_instructions()
    
    print("\n" + "="*45)
    
    # Show message preview
    test_webhook_with_sample_url()
    
    print(f"\n🔗 Your channel: https://cheeksglobal.slack.com/archives/C090FEVA18D")

if __name__ == "__main__":
    main()
