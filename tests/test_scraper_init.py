"""
Test script to verify scraper initialization
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from onlymonster_scraper import Only<PERSON>onster<PERSON><PERSON>raper

def test_scraper_init():
    """Test that the scraper can initialize properly"""
    print("Testing scraper initialization...")
    
    try:
        scraper = OnlyMonsterScraper()
        print("✓ Scraper initialized successfully")
        print("✓ Chrome WebDriver setup completed")
        
        # Test database connection
        print("✓ Database connection established")
        
        # Close the driver
        if scraper.driver:
            scraper.driver.quit()
            print("✓ Browser closed successfully")
        
        print("\nAll initialization tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Initialization failed: {e}")
        return False

if __name__ == "__main__":
    test_scraper_init()
