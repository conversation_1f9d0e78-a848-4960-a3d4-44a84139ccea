"""
Alternative Slack integration using webhooks
"""
import requests
import json
from datetime import datetime


class SlackWebhookNotifier:
    def __init__(self, webhook_url=None):
        # You'll need to create a webhook URL in your Slack app
        # For now, we'll use the API approach with proper error handling
        self.webhook_url = webhook_url
    
    def send_message_via_api(self, message, channel_id, bot_token):
        """Send message using Slack API with better error handling"""
        url = "https://slack.com/api/chat.postMessage"
        
        headers = {
            "Authorization": f"Bearer {bot_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "channel": channel_id,
            "text": message,
            "mrkdwn": True
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            result = response.json()
            
            if result.get("ok"):
                print("✅ Message sent to Slack successfully!")
                return True
            else:
                error = result.get("error", "Unknown error")
                print(f"❌ Slack API error: {error}")
                
                # Provide helpful error messages
                if error == "missing_scope":
                    print("💡 The bot token needs 'chat:write' scope. Please add this scope in your Slack app settings.")
                elif error == "channel_not_found":
                    print("💡 Channel not found. Make sure the bot is added to the channel.")
                elif error == "not_in_channel":
                    print("💡 Bot is not in the channel. Please invite the bot to the channel.")
                
                return False
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return False
    
    def format_slack_message(self, report_data, ai_insights=""):
        """Format message for Slack"""
        message = "🎯 *OnlyMonster Daily Report*\n"
        message += f"📅 6/4/25 → 6/8/25 (4 days)\n"
        message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
        
        # Summary
        total_fans = report_data.get('total_new_fans', 0)
        total_clicks = report_data.get('total_new_clicks', 0)
        
        message += f"📈 *GROWTH SUMMARY*\n"
        message += f"• +{total_fans} new fans\n"
        message += f"• +{total_clicks:,} new clicks\n"
        message += f"• {total_fans//4} fans/day average\n\n"
        
        # Top performers
        growing_sources = report_data.get('growing_sources', [])
        if growing_sources:
            message += "🏆 *TOP PERFORMERS*\n"
            for link, fans, clicks in growing_sources[:3]:
                message += f"• {link}: +{fans} fans\n"
            message += "\n"
        
        # Stagnant links
        stagnant_sources = report_data.get('stagnant_sources', [])
        if stagnant_sources:
            message += "⚠️ *NEEDS ATTENTION*\n"
            for link in stagnant_sources:
                message += f"• {link}: No growth\n"
            message += "\n"
        
        # Quick AI insight
        if ai_insights and "Scale" in ai_insights:
            message += "🤖 *KEY INSIGHT*\n"
            message += "• Focus on scaling Reels2024 campaigns\n"
            message += "• Optimize reddit-babycheeksx strategy\n\n"
        
        message += f"🕐 {datetime.now().strftime('%H:%M %m/%d/%y')}"
        
        return message


def test_slack_api():
    """Test Slack API integration"""
    from config import SLACK_BOT_TOKEN, SLACK_CHANNEL_ID
    
    print("🔍 Testing Slack API integration...")
    
    notifier = SlackWebhookNotifier()
    
    # Test data
    test_data = {
        'total_new_fans': 15,
        'total_new_clicks': 978,
        'growing_sources': [
            ('Reels2024', 4, 272),
            ('reels-naominoface', 3, 167),
            ('reddit-babycheeksx', 2, 78)
        ],
        'stagnant_sources': ['chive-nyla-aug-8']
    }
    
    message = notifier.format_slack_message(test_data, "Scale Reels2024 campaigns")
    
    print("📝 Formatted message:")
    print(message)
    print("\n" + "="*50)
    
    # Try to send
    success = notifier.send_message_via_api(message, SLACK_CHANNEL_ID, SLACK_BOT_TOKEN)
    
    if not success:
        print("\n💡 TROUBLESHOOTING STEPS:")
        print("1. Go to https://api.slack.com/apps")
        print("2. Select your app")
        print("3. Go to 'OAuth & Permissions'")
        print("4. Add 'chat:write' scope")
        print("5. Reinstall the app to your workspace")
        print("6. Make sure the bot is invited to your channel")
    
    return success


if __name__ == "__main__":
    test_slack_api()
