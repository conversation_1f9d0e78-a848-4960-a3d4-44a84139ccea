"""
Scheduler for running OnlyMonster scraping at regular intervals
"""
import schedule
import time
import logging
from datetime import datetime
from onlymonster_scraper import OnlyMonsterScraper
from analytics import TrackingAnalytics

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)

def run_scraper():
    """Run the scraper with error handling and analytics"""
    try:
        logging.info("Starting scheduled scraping...")
        scraper = OnlyMonsterScraper()
        data = scraper.run_scraping()
        logging.info(f"Scraping completed successfully! Extracted {len(data)} records")

        # Run analytics after successful scraping
        if data:
            logging.info("Running analytics...")
            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)  # Compare with 24 hours ago

            # Log key insights
            changes = analysis['changes']
            total_new_clicks = sum(changes['new_clicks'].values())
            total_new_fans = sum(changes['new_fans'].values())

            logging.info(f"Analytics: +{total_new_clicks} clicks, +{total_new_fans} fans in last 24h")

            # Print full report to console
            analytics.print_analysis_report(analysis)

    except Exception as e:
        logging.error(f"Scraping failed: {e}")

def main():
    """Main scheduler function"""
    print("OnlyMonster Scraper Scheduler Started")
    print("Scheduled to run every hour")
    print("Press Ctrl+C to stop")
    
    # Schedule the scraper to run every hour
    schedule.every().hour.do(run_scraper)
    
    # Optionally run immediately
    print("Running initial scrape...")
    run_scraper()
    
    # Keep the scheduler running
    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            print("\nScheduler stopped by user")
            break
        except Exception as e:
            logging.error(f"Scheduler error: {e}")
            time.sleep(60)

if __name__ == "__main__":
    main()
