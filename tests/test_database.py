"""
Test script for database functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import TrackingDatabase
import datetime

def test_database():
    """Test database operations"""
    print("Testing database functionality...")
    
    # Initialize database
    db = TrackingDatabase()
    
    # Test data
    test_data = [
        ("Link 1", 150, 25),
        ("Link 2", 300, 45),
        ("Link 3", 75, 12)
    ]
    
    # Insert test data
    print("Inserting test data...")
    db.insert_tracking_data(test_data)
    
    # Retrieve latest data
    print("\nLatest data:")
    latest = db.get_latest_data(5)
    for name, clicks, fans, timestamp in latest:
        print(f"  {name}: {clicks} clicks, {fans} fans ({timestamp})")
    
    # Get today's data
    today = datetime.date.today().strftime("%Y-%m-%d")
    print(f"\nData for {today}:")
    today_data = db.get_data_by_date(today)
    for name, clicks, fans, timestamp in today_data:
        print(f"  {name}: {clicks} clicks, {fans} fans ({timestamp})")
    
    print("\nDatabase test completed successfully!")

if __name__ == "__main__":
    test_database()
