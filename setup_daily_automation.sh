#!/bin/bash

# OnlyMonster Daily Report Automation Setup Script
# This script sets up automatic daily execution of the OnlyMonster report

echo "🚀 Setting up OnlyMonster Daily Report Automation"
echo "=================================================="

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Project directory: $SCRIPT_DIR"

# Make the scheduler executable
chmod +x "$SCRIPT_DIR/daily_scheduler.py"
echo "✅ Made daily_scheduler.py executable"

# Option 1: Systemd Timer (Recommended for servers - survives SSH disconnection)
echo ""
echo "🔧 Setting up systemd timer..."

# Stop any existing service
sudo systemctl stop onlymonster-daily.service 2>/dev/null || true
sudo systemctl disable onlymonster-daily.service 2>/dev/null || true

# Copy service and timer files to systemd directory
sudo cp "$SCRIPT_DIR/onlymonster-daily.service" /etc/systemd/system/
sudo cp "$SCRIPT_DIR/onlymonster-daily.timer" /etc/systemd/system/
echo "✅ Copied service and timer files to /etc/systemd/system/"

# Reload systemd and enable the timer
sudo systemctl daemon-reload
sudo systemctl enable onlymonster-daily.timer
echo "✅ Enabled onlymonster-daily timer"

# Start the timer
sudo systemctl start onlymonster-daily.timer
echo "✅ Started onlymonster-daily timer"

# Check timer status
echo ""
echo "📊 Timer Status:"
sudo systemctl status onlymonster-daily.timer --no-pager -l

echo ""
echo "📅 Next scheduled runs:"
sudo systemctl list-timers onlymonster-daily.timer --no-pager

echo ""
echo "🎯 AUTOMATION SETUP COMPLETE!"
echo "================================"
echo ""
echo "📅 Your OnlyMonster daily reports will now run automatically at 6:00 AM every day"
echo ""
echo "🔧 Management Commands:"
echo "  • Check timer:     sudo systemctl status onlymonster-daily.timer"
echo "  • View logs:       sudo journalctl -u onlymonster-daily -f"
echo "  • Stop timer:      sudo systemctl stop onlymonster-daily.timer"
echo "  • Start timer:     sudo systemctl start onlymonster-daily.timer"
echo "  • Run now:         sudo systemctl start onlymonster-daily.service"
echo "  • Disable timer:   sudo systemctl disable onlymonster-daily.timer"
echo "  • Next runs:       sudo systemctl list-timers onlymonster-daily.timer"
echo ""
echo "📱 Reports will be sent to your Slack channel automatically"
echo "🕘 Next report scheduled for: Tomorrow at 6:00 AM"
echo ""
echo "✅ Setup complete! The automation is now running in the background."

# Option 2: Cron Job (Alternative method)
echo ""
echo "📋 Alternative: Cron Job Setup"
echo "=============================="
echo "If you prefer using cron instead of systemd, run:"
echo ""
echo "crontab -e"
echo ""
echo "Then add this line:"
echo "0 6 * * * cd $SCRIPT_DIR && /usr/bin/python3 daily_scheduler.py >> $SCRIPT_DIR/cron.log 2>&1"
echo ""

# Test run option
echo "🧪 Test the automation now? (y/n)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "🚀 Running test report..."
    cd "$SCRIPT_DIR"
    python3 daily_scheduler.py &
    SCHEDULER_PID=$!
    echo "✅ Test scheduler started (PID: $SCHEDULER_PID)"
    echo "⏱️  It will run the report immediately, then wait for the next scheduled time"
    echo "🛑 Press Ctrl+C to stop the test, or let it run in the background"
fi
